import Button from "@/components/Buttons/Button";
import Spinner from "@/components/Spinners/Spinner";
import { api } from "@/helpers/api";
import { getNext6PMDate } from "@/helpers/dateHelpers";
import { cn } from "@/lib/utils";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { formatDistanceToNowStrict } from "date-fns";
import { CheckCircle } from "lucide-react";
import toast from "react-hot-toast";
import { formatCurrency } from "@/utils/currencyHelpers";

const Lottery = ({ ticketCost, disabled }) => {
    const { data: activeLottery, isLoading } = useQuery(
        api.casino.getLottery.queryOptions({
            staleTime: 30000, // 30 seconds
        })
    );
    const { data: hasEntry, isLoading: entryLoading } = useQuery(
        api.casino.checkLotteryEntry.queryOptions({
            input: { id: activeLottery?.id?.toString() || "" },
            enabled: !!activeLottery?.id,
            staleTime: 15000, // 15 seconds
        })
    );
    const lotteryDrawDate = getNext6PMDate();
    const queryClient = useQueryClient();

    const enterLotteryMutation = useMutation(
        api.casino.enterLottery.mutationOptions({
            onSuccess: () => {
                // Invalidate related queries
                queryClient.invalidateQueries({
                    queryKey: api.casino.getLottery.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.casino.checkLotteryEntry.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                toast.success("Successfully entered lottery!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );

    const handleLotteryEntry = () => {
        if (!activeLottery?.id) return;
        enterLotteryMutation.mutate({ lotteryId: activeLottery.id });
    };

    if (isLoading || entryLoading) return <Spinner center />;

    return (
        <div className="mb-4 sm:flex sm:items-center">
            <div
                className={cn(
                    "mx-auto mt-3 flex flex-col rounded-lg border-2 border-indigo-600 bg-gray-800 py-2.5 lg:block lg:w-fit lg:px-14",
                    disabled && "opacity-50"
                )}
            >
                {!activeLottery ? (
                    <p>No Lottery currently running!</p>
                ) : (
                    <>
                        <div className="mx-6 flex justify-between lg:justify-center lg:gap-10">
                            <div className="my-auto flex flex-col">
                                <p className="text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200">
                                    {" "}
                                    Current Prize Pool{" "}
                                </p>
                                <h1 className="text-center font-normal text-gray-900 text-stroke-sm text-xl leading-6 lg:text-2xl dark:text-slate-400">
                                    <span className="text-custom-yellow">
                                        {formatCurrency(activeLottery?.prizeAmount || 0)}
                                    </span>
                                </h1>
                            </div>
                            <div className="my-auto flex flex-col gap-1">
                                <p className="text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200">
                                    {" "}
                                    Entry Cost{" "}
                                </p>

                                <div className="text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-xl">
                                    <p className="text-green-500">{formatCurrency(ticketCost)}</p>
                                </div>
                            </div>
                            <div className="my-auto flex flex-col">
                                <p className="text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200">
                                    {" "}
                                    Finishes in{" "}
                                </p>

                                <p className="text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-2xl">
                                    {formatDistanceToNowStrict(lotteryDrawDate)}
                                </p>
                            </div>

                            {!hasEntry ? (
                                <Button
                                    className="font-medium! text-base! mx-auto! mt-1! lg:block! hidden! text-stroke-sm"
                                    variant="primary"
                                    disabled={disabled || enterLotteryMutation.isPending}
                                    onClick={() => handleLotteryEntry()}
                                >
                                    {enterLotteryMutation.isPending ? "Entering..." : "Buy Ticket"}
                                </Button>
                            ) : (
                                <div className="m-auto hidden gap-1 text-green-500 text-xl lg:flex">
                                    <CheckCircle className="my-auto" />
                                    Entered
                                </div>
                            )}
                        </div>
                        {!hasEntry ? (
                            <Button
                                className="font-medium! text-base! mx-auto! mt-2! lg:hidden! h-9! text-stroke-sm"
                                variant="primary"
                                disabled={disabled || enterLotteryMutation.isPending}
                                onClick={() => handleLotteryEntry()}
                            >
                                {enterLotteryMutation.isPending ? "Entering..." : "Buy Ticket"}
                            </Button>
                        ) : (
                            <div className="m-auto mt-2 flex gap-1 text-green-500 text-xl lg:hidden">
                                <CheckCircle className="my-auto" />
                                Entered
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default Lottery;

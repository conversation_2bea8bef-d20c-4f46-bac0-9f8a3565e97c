import { <PERSON><PERSON><PERSON>, ArrowUp } from "lucide-react";
import { Item, EquippedItems } from "@/types/item";

type ComparisonResult = "same" | boolean;
type ComparisonType = "modifiers" | "damage" | "armour" | "health" | "energy" | "actionPoints";

const isItemBetterThanCurrent = (
    item: Item,
    equippedItem: Item,
    type: ComparisonType
): ComparisonResult => {
    if (item?.id === equippedItem?.id) return "same";

    if (type === "modifiers") {
        const itemModifiers = item.statModifiers;
        const equippedItemModifiers = equippedItem.statModifiers;
        if (!itemModifiers && !equippedItemModifiers) return "same";
        if (!itemModifiers) return true;

        for (const [key, value] of Object.entries(itemModifiers)) {
            if (!equippedItemModifiers) return true;
            const val = (value - 1) * 100;
            if (equippedItemModifiers[key] === undefined) return "same";
            const equippedItemVal = (equippedItemModifiers[key] - 1) * 100;
            if (val > equippedItemVal) return true;
            return false;
        }
    }
    const itemStat = item[type as keyof Item] as number | null;
    const equippedItemStat = equippedItem[type as keyof Item] as number | null;

    if (!itemStat && !equippedItemStat) return "same";
    return (itemStat || 0) > (equippedItemStat || 0);
};

interface ItemComparisonArrowProps {
    item: Item;
    equippedItems?: EquippedItems;
    type?: ComparisonType;
}

export const ItemComparisonArrow = ({ item, equippedItems, type }: ItemComparisonArrowProps) => {
    const equippedItem = equippedItems?.[item.itemType] || null;
    if (!equippedItem || !type) return null;

    const isBetter = isItemBetterThanCurrent(item, equippedItem, type);
    if (isBetter === "same") return null;
    if (isBetter)
        return (
            <ArrowUp className="mb-0.5 ml-1 inline size-5 scale-125 text-green-500 transition-transform duration-200" />
        );
    return <ArrowDown className="mb-0.5 ml-1 inline size-5 scale-125 text-red-500 transition-transform duration-200" />;
};

import Button from "@/components/Buttons/Button";
import { useNavigate } from "react-router-dom";

function RegistrationDisabled() {
    const navigate = useNavigate();

    return (
        <div className="flex h-full flex-col items-center justify-center">
            <div className="relative z-40" aria-labelledby="modal-title" role="dialog" aria-modal="true">
                <div className="fixed inset-0 bg-gray-900/75 transition-opacity"></div>
                <div className="fixed inset-0 z-10 mb-60 overflow-y-auto md:mb-0">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <div className="relative mx-auto w-3/4 overflow-hidden rounded-lg bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg md:px-0">
                            <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <div className="sm:flex sm:items-start">
                                    <div className="mx-auto mt-4 flex size-12 shrink-0 items-center justify-center rounded-full bg-custom-yellow sm:mx-0 md:mt-6 md:size-14">
                                        <svg
                                            className="size-6 text-black"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            strokeWidth="2"
                                            stroke="currentColor"
                                            aria-hidden="true"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="py-4 text-center sm:mt-0 sm:ml-4 sm:text-left md:mt-3">
                                        <h3
                                            className="text-red-600 text-stroke-sm text-xl leading-6 md:text-2xl"
                                            id="modal-title"
                                        >
                                            Registration Disabled
                                        </h3>
                                        <div className="mt-2">
                                            <p className="text-gray-200 text-sm text-stroke-sm">Come back later</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="flex bg-gray-900 px-4 py-3 sm:flex-row-reverse sm:px-6">
                                <Button
                                    variant="primary"
                                    className="mx-auto! justify-center! mt-3 inline-flex w-3/4 px-4 py-2 font-medium text-base sm:mt-0 sm:ml-3 sm:text-sm"
                                    onClick={() => navigate("/login")}
                                >
                                    Back
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default RegistrationDisabled;

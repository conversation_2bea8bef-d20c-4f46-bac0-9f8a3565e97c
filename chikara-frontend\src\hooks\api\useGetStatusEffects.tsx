import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { type AppRouterClient } from "@/lib/orpc";

export type StatusEffect = Awaited<ReturnType<AppRouterClient["user"]["getStatusEffects"]>>;

/**
 * Custom hook to fetch user's status effects
 */
export const useGetStatusEffects = (options = {}) => {
    return useQuery(
        api.user.getStatusEffects.queryOptions({
            staleTime: 30 * 1000, // 30 seconds
            ...options,
        })
    );
};

export default useGetStatusEffects;

import Button from "@/components/Buttons/Button";
import { toast } from "react-hot-toast";
import { <PERSON> } from "react-router-dom";

function RegisterCodeInput({ setShowCodeInput, code, setCode, checkCode }) {
    const testKey = async (e) => {
        e.preventDefault();

        if (code.length < 8) {
            toast.error("Alpha Key is too short!");
            return;
        }

        const result = await checkCode(code);
        console.log(result);

        if (result === true) {
            setShowCodeInput(false);
        }
    };

    const handleKeyDownFirstForm = (e) => {
        if (e.key === "Enter") {
            e.preventDefault();
            testKey(e);
        }
    };

    return (
        <div className="px-4 pt-3 pb-6 text-shadow shadow-sm sm:px-10 md:py-8">
            <form className="space-y-6 " action="#" method="POST">
                <div>
                    <p className="mb-2 text-center text-gray-200">Please enter your Alpha Key to register.</p>
                    <label htmlFor="code" className="block font-medium text-gray-300 text-sm">
                        Alpha Key
                    </label>
                    <div className="mt-1">
                        <input
                            required
                            value={code}
                            id="code"
                            name="code"
                            type="text"
                            className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 text-stroke-sm shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                            onKeyDown={(e) => handleKeyDownFirstForm(e)}
                            onChange={(e) => {
                                setCode(e.target.value);
                            }}
                        />
                    </div>
                </div>
            </form>
            <div className="mt-6 flex w-full justify-center align-middle">
                <Button
                    variant="primary"
                    className="text-lg! font-medium! mx-auto w-4/5 text-stroke-sm uppercase"
                    onClick={(e) => testKey(e)}
                >
                    Continue
                </Button>
            </div>
            <div className="mt-4 text-center text-sm md:text-base">
                Already have an account?{" "}
                <Link className="text-blue-500 hover:brightness-125" to="/login">
                    Sign in
                </Link>
            </div>
        </div>
    );
}

export default RegisterCodeInput;

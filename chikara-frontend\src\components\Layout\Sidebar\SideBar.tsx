import DiscordIcon from "@/assets/icons/logos/DiscordIcon";
import Spinner from "@/components/Spinners/Spinner";
import useGetAvailableQuestList from "@/features/tasks/api/useGetAvailableQuestList";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { UTCDateMini } from "@date-fns/utc";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { usePersistStore } from "../../../app/store/stores";
import DevMenu from "../../../features/dev/components/DevMenu";
import SideBarNavigation from "./SideBarNavigation";
import SidebarProfile from "./SidebarProfile";
import { useGetStatusEffects } from "@/hooks/api/useGetStatusEffects";

export default function SideBar() {
    const [clockFormat12HR, setClockFormat12HR] = useState<boolean>(false);
    const isDev = import.meta.env.MODE === "development";
    const { data: currentUser, isLoading, error } = useFetchCurrentUser();
    const { data: availableQuests } = useGetAvailableQuestList();
    const { data: statusEffects } = useGetStatusEffects();
    const { twelveHrClock } = usePersistStore();

    const [showDevMenu, setShowDevMenu] = useState<boolean>(false);
    useEffect(() => {
        setClockFormat12HR(twelveHrClock);
    }, [twelveHrClock]);

    if (error && !error?.message) throw new Error("Server Offline");

    const openDevMenu = (): void => {
        if (!isDev) return;
        if (showDevMenu) {
            setShowDevMenu(false);
        } else {
            setShowDevMenu(true);
        }
    };

    const iconSize = "2xl:h-6 2xl:w-6 h-5 w-5";

    return (
        <div className="sidebar_shadow hidden w-48 overflow-hidden bg-linear-to-t bg-white md:block xl:w-60 2xl:w-72 dark:bg-[#171925]">
            <div className="relative mt-2 flex size-full flex-col">
                {isLoading || error ? (
                    <Spinner className="mx-auto" />
                ) : (
                    <SidebarProfile currentUser={currentUser} statusEffects={statusEffects} />
                )}
                <SideBarNavigation currentUser={currentUser} availableQuests={availableQuests} />

                <div
                    data-testid="sidebar-footer"
                    className="mt-auto border-t border-gray-500/20 bg-gray-900/50 px-4 py-3 backdrop-blur-sm"
                >
                    <div className="flex items-center justify-between mb-1.5">
                        <div className="flex gap-3">
                            <Link to="discord" className="group relative">
                                <button className="flex h-9 w-9 items-center justify-center rounded-lg bg-indigo-600/80 text-indigo-200 shadow-lg ring-1 ring-white/10 transition-all hover:bg-indigo-500 hover:text-white hover:ring-white/20 hover:shadow-indigo-500/25">
                                    <span className="sr-only">Join Discord</span>
                                    <DiscordIcon className={iconSize} aria-hidden="true" />
                                </button>
                                <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-gray-300 opacity-0 transition-opacity group-hover:opacity-100">
                                    Discord
                                </span>
                            </Link>

                            <a
                                href="https://chikaraacademymmo.wiki.gg/"
                                target="_blank"
                                rel="noreferrer"
                                className="group relative"
                            >
                                <button className="flex h-9 w-9 items-center justify-center rounded-lg bg-blue-600/80 text-blue-200 shadow-lg ring-1 ring-white/10 transition-all hover:bg-blue-500 hover:text-white hover:ring-white/20 hover:shadow-blue-500/25">
                                    <span className="sr-only">Wiki</span>
                                    <img
                                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/MhJCUZW.png`}
                                        className="h-4 w-4"
                                        alt="Wiki"
                                    />
                                </button>
                                <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-gray-300 opacity-0 transition-opacity group-hover:opacity-100">
                                    Wiki
                                </span>
                            </a>
                        </div>

                        <div className="flex flex-col items-end gap-1">
                            <div
                                className={cn(
                                    "text-sm font-medium text-blue-300 dark:text-blue-400",
                                    isDev && "cursor-pointer hover:text-blue-200"
                                )}
                                onClick={() => openDevMenu()}
                            >
                                {clockFormat12HR
                                    ? format(new UTCDateMini(), "h:mm a")
                                    : format(new UTCDateMini(), "H:mm")}
                            </div>
                            <p
                                className={cn("text-xs text-gray-400", isDev && "cursor-pointer hover:text-purple-400")}
                                onClick={() => openDevMenu()}
                            >
                                v{import.meta.env.VITE_PACKAGE_VERSION}
                            </p>
                        </div>
                    </div>
                </div>

                {showDevMenu && <DevMenu setShowDevMenu={setShowDevMenu} />}
            </div>
        </div>
    );
}

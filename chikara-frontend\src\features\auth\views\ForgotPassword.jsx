import { useState } from "react";
import { <PERSON> } from "react-router-dom";

import Button from "@/components/Buttons/Button";
import ButtonSpinner from "@/components/Spinners/ButtonSpinner";
import { forgetPassword } from "@/lib/auth-client";
import { toast } from "react-hot-toast";

export default function ForgotPassword() {
    const [email, setEmail] = useState("");
    const [emailSuccess, setEmailSuccess] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const forgotPassword = async (e) => {
        e.preventDefault();
        if (email.length < 5) {
            toast.error("Email address is too short!");
            return;
        }
        // email validation regex
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            toast.error("Invalid email address!");
            return;
        }

        setIsLoading(true);

        const { data, error } = await forgetPassword({
            email,
        });

        if (data && !error) {
            if (emailSuccess) {
                toast.success("Email resent!");
            }
            setEmailSuccess(true);
        } else {
            toast.error("Server error! Try again later");
            console.log(error);
        }
        setIsLoading(false);
    };

    const handleKeyDown = (event) => {
        if (event.key === "Enter") {
            event.preventDefault();
            forgotPassword(event);
        }
    };

    return (
        <>
            {emailSuccess ? (
                <div className="max-h-full overflow-y-auto overflow-x-hidden px-4 pt-3 pb-6 md:px-6">
                    <div className="sm:mx-auto sm:w-full sm:max-w-md">
                        <h3 className="mb-5 text-center text-indigo-400 md:text-xl">
                            Thanks, please check your email for instructions on how to reset your password
                        </h3>
                        <p className="text-center text-sm md:text-sm">
                            If you haven&apos;t received an email in 5 minutes, check your spam or{" "}
                            <span
                                className="cursor-pointer text-blue-500"
                                onClick={(e) => {
                                    forgotPassword(e);
                                }}
                            >
                                resend.
                            </span>
                        </p>
                        <Link to="/login" className="mt-12 flex w-full justify-center align-middle">
                            <Button
                                variant="primary"
                                className="text-lg! font-medium! mx-auto w-4/5 text-stroke-sm uppercase"
                            >
                                Back
                            </Button>
                        </Link>
                    </div>
                </div>
            ) : (
                <>
                    <div className="max-h-full overflow-y-auto overflow-x-hidden px-4 pt-3 pb-6 md:px-6">
                        <p className="mb-5 text-center text-sm">
                            Enter the email address associated with your account and we&apos;ll send you a link to reset
                            your password.
                        </p>
                        <form className="space-y-6">
                            <div>
                                <label htmlFor="email" className="block font-medium text-gray-300 text-sm">
                                    Email address
                                </label>
                                <div className="mt-1">
                                    <input
                                        required
                                        id="email"
                                        name="email"
                                        type="email"
                                        autoComplete="email"
                                        className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 text-stroke-sm shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                                        onKeyDown={(e) => handleKeyDown(e)}
                                        onChange={(e) => {
                                            setEmail(e.target.value);
                                        }}
                                    />
                                </div>
                            </div>
                        </form>
                        <div className="mt-6 flex w-full justify-center align-middle">
                            <Button
                                variant="primary"
                                className="text-lg! font-medium! mx-auto w-4/5 text-stroke-sm uppercase"
                                onClick={(e) => forgotPassword(e)}
                            >
                                {isLoading ? <ButtonSpinner /> : "Continue"}
                            </Button>
                        </div>
                        <Link to="/login">
                            <p className="-mb-1 mt-4 text-center text-blue-500 text-sm hover:brightness-125">
                                Return to login
                            </p>
                        </Link>
                    </div>
                </>
            )}
        </>
    );
}

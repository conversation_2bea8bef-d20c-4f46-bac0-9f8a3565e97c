import hpIcon from "@/assets/icons/UI/HPicon.png";
import { cn } from "@/lib/utils";
import { useState } from "react";
import FullscreenModal from "./FullscreenModal";
import blueFrame from "./blueframe.png";
import continueTxt from "./continue.png";
import greenFrame from "./greenframe.png";
import levelBadgeImg from "./levelBadge.png";
import lvlTxt from "./levelup.png";
import lightImg from "./light.png";
import lineLeftImg from "./lineLeft.png";
import lineRightImg from "./lineRight.png";
import rewardsTxt from "./rewards.png";

interface LevelupModalProps {
    newLevel: number;
    setLevelupValue: (value: number | null) => void;
}

const LevelupModal = ({ newLevel, setLevelupValue }: LevelupModalProps) => {
    const [canClick, setCanClick] = useState(false);
    const levelUpUnlocks: Record<number, string[]> = {
        3: ["Unlocked Daily Tasks", "Unlocked Mission Board"],
        4: ["Unlocked Part-Time Jobs"],
        5: ["Unlocked Talents", "Unlocked PvP Battles"],
        7: ["Unlocked Crafting Workshop", "Unlocked Shoko's General Goods"],
        8: ["Unlocked Courses"],
        15: ["Unlocked Otake's Rare Goods"],
    };
    const currentUnlocks = levelUpUnlocks[newLevel] || null;
    const showTalentPoints = newLevel > 4;

    setTimeout(() => {
        setCanClick(true);
    }, 2000);

    const handleClick = () => {
        if (canClick) {
            setLevelupValue(null);
        }
    };

    return (
        <FullscreenModal onClick={() => handleClick()}>
            <div className="animate-pop">
                <div className="ribbonPurple -translate-x-1/2 absolute top-36 left-1/2 flex h-24 w-md scale-[0.7] flex-col">
                    <img className="-translate-x-1/2 absolute top-5 left-1/2 h-10 w-auto" src={lvlTxt} alt="" />
                    {/* <p className="text-[2.7rem] text-white uppercase text-stroke-sm absolute top-2.5 left-[9.4rem] w-full -translate-x-1/2 ">
            LEVEL UP!
          </p> */}
                </div>
                <div className="-translate-x-1/2 absolute top-64 left-1/2">
                    <img className="h-44 w-auto" src={levelBadgeImg} alt="" />

                    <p className="-translate-x-1/2 -translate-y-1/2 absolute top-[45%] left-1/2 text-7xl text-stroke-s-md text-white">
                        {newLevel}
                    </p>
                </div>
                <img
                    className="-translate-x-1/2 -z-10 absolute top-38 left-48 h-96 w-160 scale-150 md:left-1/2"
                    src={lightImg}
                    alt=""
                />
            </div>
            <div
                className={cn(
                    currentUnlocks?.length > 0 ? "bottom-44" : "bottom-56",
                    "anim-delay absolute flex w-full animate-pop flex-col"
                )}
            >
                <div className="mx-auto mb-2 flex gap-4">
                    <img className="my-auto h-4 w-auto" src={lineLeftImg} alt="" />
                    <img src={rewardsTxt} alt="" className="h-5 w-auto" />
                    {/* <p className="text-white text-stroke-sm text-xl uppercase">Rewards</p> */}
                    <img className="my-auto h-4 w-auto" src={lineRightImg} alt="" />
                </div>
                <div
                    className={cn(
                        showTalentPoints ? "grid-cols-2" : "grid-cols-1",
                        "mx-auto grid w-56 grid-flow-col place-items-center gap-4 text-center"
                    )}
                >
                    {/* <div className="mx-auto flex flex-col gap-2 rounded-lg bg-black/25 px-4 py-2">
              <img src={hpIcon} alt="" className="h-14 w-auto px-1.5" />
              <p className="text-white text-2xl text-stroke-sm">50</p>
            </div> */}
                    <div className="relative">
                        <img src={greenFrame} className="h-32 w-auto" alt="" />
                        <img src={hpIcon} alt="" className="-translate-x-1/2 absolute top-5 left-1/2 h-14 w-auto" />
                        <p className="-translate-x-1/2 absolute bottom-1 left-1/2 text-2xl text-stroke-sm text-white">
                            +50
                        </p>
                    </div>
                    {showTalentPoints && (
                        <div className="relative">
                            <img src={blueFrame} className="h-32 w-auto" alt="" />
                            <img
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/talentpoints.png`}
                                alt=""
                                className="-translate-x-1/2 absolute top-4 left-1/2 h-16 w-auto"
                            />
                            <p className="-translate-x-1/2 absolute bottom-1 left-1/2 text-2xl text-stroke-sm text-white">
                                1
                            </p>
                        </div>
                    )}

                    {/* <div className="mx-auto flex flex-col gap-1 rounded-lg bg-black/25 px-4 py-2 w-full h-28">
              <img
                className="h-16 w-auto px-2"
                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/talentpoints.png`}
                alt="Talent Points"
              />
              <p className="text-white text-2xl text-stroke-sm">1</p>
            </div> */}
                </div>
                {currentUnlocks?.length > 0 && (
                    <div className="mx-auto mt-4 rounded-lg bg-black/25 px-4 py-2 text-center">
                        {currentUnlocks?.map((unlock, index) => (
                            <p key={index} className="text-2xl text-custom-yellow text-stroke-sm">{unlock}</p>
                        ))}
                    </div>
                )}
            </div>
            <img className="-translate-x-1/2 absolute bottom-24 left-1/2 h-[1.8rem] w-auto" src={continueTxt} alt="" />

            {/* <p className="text-white text-stroke-sm text-2xl absolute bottom-24 left-1/2 -translate-x-1/2">
          Tap to Continue
        </p> */}
        </FullscreenModal>
    );
};

export default LevelupModal;

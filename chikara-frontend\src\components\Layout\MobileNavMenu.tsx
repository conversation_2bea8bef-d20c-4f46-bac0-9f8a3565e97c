import currency2Img from "@/assets/icons/UI/currency2.png";
import expImg from "@/assets/icons/UI/expBG.png";
import yenImg from "@/assets/icons/UI/yen.png";
import DiscordIcon from "@/assets/icons/logos/DiscordIcon";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import NotificationBadge from "@/components/NotificationBadge";
import { handleLogout } from "@/helpers/handleLogout";
import { mobileTopNavItems } from "@/helpers/navItems";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { UTCDateMini } from "@date-fns/utc";
import { Popover, Transition } from "@headlessui/react";
import { format } from "date-fns";
import { LogOut } from "lucide-react";
import { Fragment, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { usePersistStore } from "../../app/store/stores";
import DevMenu from "../../features/dev/components/DevMenu";
import StatusEffects from "../StatusEffects";
import ShrineBuff from "./ShrineBuff";
import type { StatusEffect } from "@/hooks/api/useGetStatusEffects";
import type { User } from "@/types/user";

const EXPStatDisplay = ({
    icon,
    statValue,
    level,
    percentEXP,
}: {
    icon: string;
    statValue: string;
    level: number;
    percentEXP: number;
}) => {
    const { MAX_LEVEL_CAP } = useGameConfig();
    const isMaxLevel = level >= MAX_LEVEL_CAP;
    return (
        <div className="mt-[-0.2rem] relative mx-auto flex h-[1.3rem] w-full rounded-md border-2 border-black bg-slate-700">
            <div className={cn("-left-2 -top-0.5 absolute z-10 h-7 w-auto")}>
                <div className="relative size-full">
                    <img className="z-15 h-7 w-6" src={icon} alt="" />
                    <p className="-translate-x-1/2 -translate-y-1/2 absolute top-[42%] left-1/2 font-bold font-body text-stroke-s-sm text-stroke-sm text-white text-xs">
                        {level}
                    </p>
                </div>
            </div>
            <p className="z-10 my-auto ml-2 grow text-center text-slate-200 text-stroke-sm text-xs">
                {isMaxLevel ? "MAX" : statValue}
            </p>
            <div
                style={{ width: isMaxLevel ? "100%" : percentEXP + "%" }}
                className="absolute z-0 h-full rounded-md border-2 border-transparent bg-blue-700"
            ></div>
        </div>
    );
};

const StatusEffectContainer = ({
    statusEffects,
    borderLabel = null,
}: {
    statusEffects: StatusEffect[];
    borderLabel: string | null;
}) => {
    return (
        <div className="relative mt-4 flex h-fit flex-1 scale-95 flex-col rounded-lg border-2 border-gray-200 bg-white py-1 2xl:scale-100 2xl:py-1.5 dark:border-red-500 dark:bg-gray-800">
            <p className="-translate-x-1/2 -top-3 absolute left-1/2 rounded-md border border-white bg-slate-800 px-2 text-red-500 text-xs uppercase tracking-wide">
                {borderLabel}
            </p>

            <StatusEffects currentEffects={statusEffects} />
        </div>
    );
};

const SocialLinks = () => {
    return (
        <div className="mt-auto mb-1 flex gap-4 px-4 pt-2.5 md:pb-1">
            <div className="flex items-center">
                <Link to="/discord">
                    <button className="relative shrink-0 rounded-md bg-indigo-800 p-1 text-indigo-200 shadow-lg ring-2 ring-black/20 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600">
                        <span className="sr-only">Join Discord</span>
                        <DiscordIcon className="size-6" aria-hidden="true" />
                    </button>
                </Link>
            </div>

            <div className="size-8 rounded-md bg-blue-600">
                <a
                    className="flex size-full flex-col"
                    href="https://chikaraacademymmo.wiki.gg"
                    target="_blank"
                    rel="noreferrer"
                >
                    <img
                        className="size-full opacity-80"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/MhJCUZW.png`}
                        alt="Wiki"
                    />
                </a>
            </div>
        </div>
    );
};

const LogoutButton = () => {
    return (
        <div className="mt-4 flex items-center justify-center">
            <button
                className="flex w-full items-center justify-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 font-medium text-gray-700 text-sm hover:bg-gray-200 focus:outline-hidden focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                onClick={handleLogout}
            >
                <LogOut className="mr-2 size-5" />
                Logout
            </button>
        </div>
    );
};

export default function MobileNavbarPopover({
    currentUser,
    percentEXP,
    unreadNotifications,
    unreadMessages,
    statusEffects,
    unreadNewsPosts,
}: {
    currentUser: User;
    percentEXP: number;
    unreadNotifications: { unread: number };
    unreadMessages: { unread: number };
    statusEffects: StatusEffect[];
    unreadNewsPosts: number;
}) {
    const [showDevMenu, setShowDevMenu] = useState(false);
    const [clockFormat12HR, setClockFormat12HR] = useState(false);
    const { twelveHrClock } = usePersistStore();
    const userMobileNavigation = mobileTopNavItems(currentUser?.userType);
    const isDev = import.meta.env.MODE === "development";

    useEffect(() => {
        setClockFormat12HR(twelveHrClock);
    }, [twelveHrClock]);

    const openDevMenu = () => {
        if (!isDev) return;
        if (showDevMenu) {
            setShowDevMenu(false);
        } else {
            setShowDevMenu(true);
        }
    };

    return (
        <Transition.Root as={Fragment}>
            <div className="lg:hidden">
                <Transition.Child
                    as={Fragment}
                    enter="duration-150 ease-out"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="duration-150 ease-in"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <Popover.Overlay className="fixed inset-0 z-20 bg-black/50" />
                </Transition.Child>

                <Transition.Child
                    as={Fragment}
                    enter="duration-150 ease-out"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="duration-150 ease-in"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                >
                    <Popover.Panel
                        focus
                        className="absolute top-12 right-0 z-30 mx-auto w-[62%] max-w-xl origin-top p-2 transition"
                    >
                        <div className="rounded-lg border-2 border-black bg-slate-900/95 shadow-lg">
                            {showDevMenu && <DevMenu setShowDevMenu={setShowDevMenu} />}
                            <div className="flex flex-col pt-3 pb-1">
                                <ShrineBuff isMobile />
                                <div className="flex flex-col items-center gap-2.5 px-4">
                                    <div className="flex size-full items-center justify-center gap-4">
                                        <Link to={`/profile/${currentUser?.id}`}>
                                            <div className="min-w-12">
                                                <DisplayAvatar className="h-12 rounded-full" src={currentUser} />
                                            </div>
                                        </Link>
                                        <Link as="div" className="max-h-6 w-3/5" to={`/profile/${currentUser?.id}`}>
                                            <div className="-mt-2 flex w-full flex-1 flex-col justify-center gap-0.5 text-center">
                                                <div
                                                    className={cn(
                                                        currentUser?.username?.length > 12 ? "text-xs" : "text-sm",
                                                        "font-medium text-gray-200"
                                                    )}
                                                >
                                                    {currentUser?.username}{" "}
                                                    <span className="text-[0.6rem]! font-medium text-gray-400">
                                                        #{currentUser?.id}
                                                    </span>
                                                </div>
                                                <EXPStatDisplay
                                                    icon={expImg}
                                                    statValue={`${currentUser?.xp} / ${currentUser?.xpForNextLevel}`}
                                                    level={currentUser?.level}
                                                    percentEXP={percentEXP}
                                                />
                                            </div>
                                        </Link>
                                    </div>

                                    <div className="mx-auto flex gap-1">
                                        <Link to="/bank">
                                            <div className="-skew-x-6 mt-1 flex rounded-md border-2 border-black bg-linear-to-b from-blue-600 to-blue-800 px-3">
                                                <p className="mx-auto my-0.5 skew-x-6 text-center text-md text-stroke-sm text-white">
                                                    <img
                                                        className="mr-1 mb-1 inline aspect-square h-5 w-auto"
                                                        src={yenImg}
                                                        alt=""
                                                    />
                                                    {currentUser?.cash}
                                                </p>
                                            </div>
                                        </Link>
                                        <div className="-skew-x-6 mt-1 flex rounded-md border-2 border-black bg-linear-to-b from-yellow-500 to-yellow-700 px-3">
                                            <p className="mx-auto my-0.5 skew-x-6 text-center text-md text-stroke-sm text-white">
                                                <img
                                                    className="mr-1 mb-1 inline aspect-square h-5 w-auto"
                                                    src={currency2Img}
                                                    alt=""
                                                />
                                                {currentUser?.gangCreds}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div className="mx-auto flex w-[95%] flex-col">
                                    <div className="flex">
                                        {statusEffects && statusEffects.length > 0 && (
                                            <StatusEffectContainer
                                                statusEffects={statusEffects}
                                                borderLabel="Injuries"
                                            />
                                        )}
                                        {/* {activeBuffs && activeBuffs.length > 0 && (
                      <StatusEffectContainer statusEffects={statusEffects} borderLabel="Buffs" />
                    )} */}
                                    </div>
                                </div>

                                <div className="mt-3 space-y-0 border-gray-800 border-y px-2 sm:px-4">
                                    {userMobileNavigation.map((item) =>
                                        item.external ? (
                                            <Popover.Button
                                                key={item.name}
                                                target="_blank"
                                                as="a"
                                                rel="noreferrer"
                                                href={item.construction ? null : item.href}
                                                className={cn(
                                                    "flex gap-3 rounded-md px-3 py-2 font-medium text-base",
                                                    item.construction ? "text-white opacity-35" : "text-gray-200"
                                                )}
                                            >
                                                <div className="w-1/5 sm:w-1/6">
                                                    <img src={item.icon} className="h-8 w-auto" alt="" />
                                                </div>

                                                <p className="my-auto sm:text-xl">{item.name}</p>
                                            </Popover.Button>
                                        ) : (
                                            <Popover.Button
                                                key={item.name}
                                                as={Link}
                                                to={item.construction ? null : item.href}
                                                className={cn(
                                                    "flex gap-3 rounded-md px-3 py-2 font-medium text-base sm:gap-0",
                                                    item.construction ? "text-white opacity-35" : "text-gray-200"
                                                )}
                                            >
                                                <div className="w-1/5 sm:w-1/6">
                                                    <img src={item.icon} className="h-8 w-auto" alt="" />
                                                </div>

                                                <p className="my-auto sm:text-xl">{item.name}</p>
                                                {item.name === "Events" && (
                                                    <NotificationBadge
                                                        square
                                                        amount={unreadNotifications?.unread}
                                                        className="my-auto ml-auto"
                                                    />
                                                )}
                                                {item.name === "Inbox" && (
                                                    <NotificationBadge
                                                        square
                                                        amount={unreadMessages?.unread}
                                                        className="my-auto ml-auto"
                                                    />
                                                )}
                                                {item.name === "News" && (
                                                    <NotificationBadge
                                                        square
                                                        amount={unreadNewsPosts}
                                                        className="my-auto ml-auto"
                                                    />
                                                )}
                                            </Popover.Button>
                                        )
                                    )}
                                    {currentUser?.userType === "admin" && <LogoutButton />}
                                </div>

                                <SocialLinks />
                                <div className="absolute right-6 bottom-5">
                                    <div
                                        className={cn(
                                            "ml-auto py-4 text-sm dark:text-indigo-500",
                                            isDev && "cursor-pointer"
                                        )}
                                        onClick={() => openDevMenu()}
                                    >
                                        {clockFormat12HR
                                            ? format(new UTCDateMini(), "h:mm a")
                                            : format(new UTCDateMini(), "H:mm")}
                                    </div>
                                </div>
                                {}
                                <p
                                    className={cn(
                                        "absolute right-6 bottom-5 text-custom-yellow text-xs",
                                        isDev && "cursor-pointer"
                                    )}
                                    onClick={() => openDevMenu()}
                                >
                                    v{import.meta.env.VITE_PACKAGE_VERSION}
                                </p>
                            </div>
                        </div>
                    </Popover.Panel>
                </Transition.Child>
            </div>
        </Transition.Root>
    );
}
